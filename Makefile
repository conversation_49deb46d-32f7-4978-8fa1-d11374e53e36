.PHONY: build image docker-build docker-push docker-run docker-stop docker-clean compose-up compose-down compose-logs

all: image

gobuild:
	@sh scripts/gobuild.sh

web:
	@echo "web build success"

image: gobuild web
	@echo "image build success"

build:
	docker build -t zjl-server:1 . && \
	docker tag zjl-server:1 registry.rcztcs.com/zjl/zjl-server:1 && \
	echo Docker image build success: zjl-server:1 && \
	docker push registry.rcztcs.com/zjl/zjl-server:1 && \
	echo Docker image pushed: registry.rcztcs.com/zjl/zjl-server:1


run:
	@echo Starting services with specific version...
	@echo 请输入要启动的镜像版本 (例如: v1.0.0):
	@set /p VERSION= && \
	if "!VERSION!"=="" ( \
		echo 版本不能为空 && \
		exit /b 1 \
	) && \
	powershell -Command "(Get-Content docker-compose.yaml) -replace 'registry.rcztcs.com/zjl:.*', 'registry.rcztcs.com/zjl:!VERSION!' | Set-Content docker-compose.temp.yaml" && \
	docker-compose -f docker-compose.temp.yaml up -d && \
	del docker-compose.temp.yaml && \
	echo Services started with version: !VERSION!